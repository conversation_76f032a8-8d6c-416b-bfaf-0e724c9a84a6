using NeuroAnalysis,Statistics,StatsBase,FileIO,Images,Plots,LsqFit,FFTW,CSVFiles,Serialization

# Expt info
disk = "/media/vcl/vcl003/"
subject = "AF3"  # Animal

recordSession = "002"
testId = ["003","004","005","006"]

recordPlane = "001"
delays = collect(-0.066:0.033:0.4)
print(collect(delays))

lbTime = 0.198
ubTime = 0.330
blkTime = 0.099
respThres = 0.25

siteId=[]
for i =1:size(testId,1)
    siteid = join(["$(recordSession)_", testId[i], "_$(recordPlane)"])
    push!(siteId,siteid)
end

dataFolder = joinpath.(disk,subject, "2P_analysis", join(["U",recordSession]))
dataExportFolder = joinpath.(disk,subject, "2P_analysis", join(["U",recordSession]), siteId, "DataExport/FourierRFresults")
resultFolder = joinpath.(disk,subject, "2P_analysis", join(["U",recordSession]), "_Summary", "DataExport")
resultFolderPlot = joinpath.(disk,subject, "2P_analysis", join(["U",recordSession]), "_Summary", join(["plane_",recordPlane]),"0. Original maps","Fourier")
isdir(resultFolder) || mkpath(resultFolder)
isdir(resultFolderPlot) || mkpath(resultFolderPlot)
dataFile=[]
for i=1:size(testId,1)
    datafile=matchfile(Regex("[A-Za-z0-9]*[A-Za-z0-9]*_[A-Za-z0-9]*_[A-Za-z0-9]*_fourier_dataset.jls"), dir=dataExportFolder[i],join=true)
    push!(dataFile, datafile)
    # matched_files = matchfile(r"*_fourier_dataset.jls", dir=dataExportFolder[i], join=true)
    # push!(dataFile, matched_files[1])  # ✅ correct: single file

end
datasets = []
for f in dataFile
    println("Loading file: $f")
    data = open(join(f), "r") do io
        deserialize(io)
    end
    push!(datasets, data)  # or data["dataset"], depending on how you saved it
end

# dataFile[1]
## Join Results from L, M, S, and achromatic expts.)
datasetJoin = sbxjoinhartleyFourier(datasets)
serialize_file = joinpath(resultFolder, join([subject,"_",recordSession,"_",recordPlane,"_thres",respThres,"_join_fourier_results.jls"]))
serialize_data = datasetJoin
open(serialize_file, "w") do io
    serialize(io, serialize_data)
end
for u in sort(collect(keys(datasetJoin["kern"])))
    # u=10
    colors =["L", "M", "S", "A"]
    kern = datasetJoin["kern"][u]
    replace!(kern, -Inf=>0)
    maxmag = maximum(abs.(kern))
    kern = kern ./ maxmag   # Kernal is normalized by maximum of all kernals (L, M, S, A) for this cell
    # kern= normalized(kern;equal=false)
    delta = datasetJoin["kdelta"]
    imagesize = size(kern)[1]
    truestimsz = 12   # Arbitrary
    colorbar_hack = zeros(size(kern))
    xylim = [0,round(truestimsz,digits=1)]
    xy = range(xylim...,length=imagesize)

    p = Plots.plot(layout=(1,5),legend=false,size=(1650,600))
    # Plots.bar!(p,subplot=1,dataset["color"],ucex,frame=:zerolines)
    foreach(c->Plots.heatmap!(p,subplot=c,xy,xy,kern[:,:,c],aspect_ratio=:equal,frame=:grid,
    color=:bwr,clims=(-1,1),xlims=xylim,ylims=xylim,xticks=[],yticks=[],yflip=true,xlabel=string(colors[c]),title="Cell_$(u)_Fourier"),1:4)
    heatmap!(p,subplot=5,xy,xy,colorbar_hack[:,:,1],aspect_ratio=:equal,frame=:grid,xticks=[],yticks=[],color=:bwr,clims=(-maxmag,maxmag),colorbar=:left) # add colorbar
    foreach(c->Plots.plot!(p,subplot=c,[6], seriestype="vline", linecolor=:gray, linestyle=:dot, linewidth=3, linealpha=1, xticks=([6],["0"]), label=""),1:4)
    foreach(c->Plots.plot!(p,subplot=c,[6], seriestype="hline", linecolor=:gray, linestyle=:dot, linewidth=3, linealpha=1, label=""),1:4)
    foreach(c->Plots.plot!(p,subplot=c,yticks=([6],["0"])),1)
    # :diverging_bwr_40_95_c42_n256   RdBu_5
    # foreach(c->Plots.plot!(p,subplot=c+1,[0.2,0.4,0.6,0.8,1.0,1.2,1.4], seriestype="vline", linecolor=:gray, linestyle=:dot, linewidth=1, linealpha=0.5, xticks=([0,0.4,0.8,1.2,1.6],["-0.8","-0.4","0","0.4","0.8"]), label=""),1:4)
    # foreach(c->Plots.plot!(p,subplot=c+1,[0.2,0.4,0.6,0.8,1.0,1.2,1.4], seriestype="hline", linecolor=:gray, linestyle=:dot, linewidth=1, linealpha=0.5, label=""),1:4)
    # foreach(c->Plots.plot!(p,subplot=c+1,yticks=([0,0.4,0.8,1.2,1.6],["-0.8","-0.4","0","0.4","0.8"])),1)

    # foreach(c->Plots.plot!(p,subplot=c+1,[0.4,0.8,1.2,1.6,2.0], seriestype="vline", linecolor=:gray, linestyle=:dot, linewidth=1, linealpha=0.5, xticks=([0,0.6,1.2,1.8,2.4],["-1.2","-0.6","0","0.6","1.2"]), label=""),1:4)
    # foreach(c->Plots.plot!(p,subplot=c+1,[0.4,0.8,1.2,1.6,2.0], seriestype="hline", linecolor=:gray, linestyle=:dot, linewidth=1, linealpha=0.5, label=""),1:4)
    # foreach(c->Plots.plot!(p,subplot=c+1,yticks=([0,0.6,1.2,1.8,2.4],["-1.2","-0.6","0","0.6","1.2"])),1)
    p
    savefig(joinpath(resultFolderPlot,join([subject,"_U",recordSession,"_Plane",recordPlane, "_Cell",u,".png"])))
end

## Plot example cell

##
